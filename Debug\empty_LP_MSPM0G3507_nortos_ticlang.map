******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Tue Jul 29 12:19:14 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00002895


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00002f88  0001d078  R  X
  SRAM                  20200000   00008000  0000041f  00007be1  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00002f88   00002f88    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00002cb0   00002cb0    r-x .text
  00002d70    00002d70    000001d8   000001d8    r-- .rodata
  00002f48    00002f48    00000040   00000040    r-- .cinit
20200000    20200000    00000221   00000000    rw-
  20200000    20200000    000001fa   00000000    rw- .bss
  202001fc    202001fc    00000025   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00002cb0     
                  000000c0    00000644     libc.a : k_rem_pio2.c.obj (.text.__kernel_rem_pio2)
                  00000704    00000458            : s_sin.c.obj (.text.sin)
                  00000b5c    000002f8            : s_atan.c.obj (.text.atan)
                  00000e54    000001b4     control.o (.text.TIMA0_IRQHandler)
                  00001008    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000119a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000119c    00000168     libc.a : k_sin.c.obj (.text.__kernel_sin)
                  00001304    00000150            : k_cos.c.obj (.text.__kernel_cos)
                  00001454    00000144            : s_floor.c.obj (.text.floor)
                  00001598    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  000016a4    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  0000178c    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00001870    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00001948    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00001a20    000000c4     driverlib.a : dl_timer.o (.text.DL_Timer_initPWMMode)
                  00001ae4    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00001b70    0000008c     libc.a : s_floorf.c.obj (.text.floorf)
                  00001bfc    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00001c7e    00000002     --HOLE-- [fill = 0]
                  00001c80    00000080     driverlib.a : dl_timer.o (.text.DL_TimerA_initPWMMode)
                  00001d00    0000007c     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001d7c    0000007c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001df8    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00001e74    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00001ee8    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00001ef0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00001f64    00000074     board.o (.text.delay_us)
                  00001fd8    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002040    00000068     libc.a : s_roundf.c.obj (.text.roundf)
                  000020a8    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000210a    00000002     --HOLE-- [fill = 0]
                  0000210c    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  0000216e    00000002     --HOLE-- [fill = 0]
                  00002170    00000060     control.o (.text.angle_to_pwm_180)
                  000021d0    00000060     control.o (.text.angle_to_pwm_270)
                  00002230    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_1_init)
                  0000228c    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  000022e4    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  0000233a    00000002     --HOLE-- [fill = 0]
                  0000233c    00000054     empty.o (.text.main)
                  00002390    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000023dc    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00002426    00000002     --HOLE-- [fill = 0]
                  00002428    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00002470    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  000024b8    00000044     led.o (.text.LED_Flash)
                  000024fc    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00002540    00000044     control.o (.text.base_x)
                  00002584    00000044     control.o (.text.base_y)
                  000025c8    00000040     libclang_rt.builtins.a : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00002608    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00002644    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00002680    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  000026ba    00000002     --HOLE-- [fill = 0]
                  000026bc    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000026f6    00000002     --HOLE-- [fill = 0]
                  000026f8    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00002730    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00002764    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00002798    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  000027c4    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  000027f0    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  0000281c    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00002844    00000028     ti_msp_dl_config.o (.text.DL_SYSTICK_init)
                  0000286c    00000028     motor.o (.text.Set_PWM)
                  00002894    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000028bc    00000022     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000028de    00000002     --HOLE-- [fill = 0]
                  000028e0    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00002900    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  0000291e    00000002     --HOLE-- [fill = 0]
                  00002920    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  0000293c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00002958    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00002974    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00002990    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000029ac    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000029c8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000029e0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000029f8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00002a10    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00002a28    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00002a40    00000018     led.o (.text.DL_GPIO_togglePins)
                  00002a58    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00002a70    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00002a88    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00002aa0    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00002ab8    00000018     empty.o (.text.DL_Timer_startCounter)
                  00002ad0    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00002ae8    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00002b00    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00002b16    00000016     libc.a : k_rem_pio2.c.obj (.text.OUTLINED_FUNCTION_0)
                  00002b2c    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00002b3c    00000016     board.o (.text.delay_ms)
                  00002b52    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00002b68    00000014     led.o (.text.DL_GPIO_clearPins)
                  00002b7c    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00002b90    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00002ba4    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_enableMFCLK)
                  00002bb8    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00002bcc    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00002be0    00000014     led.o (.text.LED_ON)
                  00002bf4    00000014     led.o (.text.LED_Toggle)
                  00002c08    00000012     control.o (.text.DL_Timer_getPendingInterrupt)
                  00002c1a    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00002c2c    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00002c3e    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00002c50    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00002c60    00000010     ti_msp_dl_config.o (.text.DL_SYSTICK_enable)
                  00002c70    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00002c80    00000010     libc.a : k_rem_pio2.c.obj (.text.OUTLINED_FUNCTION_2)
                  00002c90    0000000e            : s_sin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00002c9e    00000002     --HOLE-- [fill = 0]
                  00002ca0    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00002cb0    0000000e     libc.a : s_sin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00002cbe    0000000e     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00002ccc    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00002cd6    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00002ce0    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00002cf0    0000000a     libc.a : s_roundf.c.obj (.text.OUTLINED_FUNCTION_0)
                  00002cfa    00000002     --HOLE-- [fill = 0]
                  00002cfc    00000010     libclang_rt.builtins.a : addsf3.S.obj (.tramp.__aeabi_fadd.1)
                  00002d0c    0000000a     libc.a : k_rem_pio2.c.obj (.text.OUTLINED_FUNCTION_1)
                  00002d16    00000002     --HOLE-- [fill = 0]
                  00002d18    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00002d20    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00002d28    00000006     libc.a : k_cos.c.obj (.text.OUTLINED_FUNCTION_0)
                  00002d2e    00000006            : k_sin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00002d34    00000006            : s_sin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00002d3a    00000006            : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00002d40    00000006            : s_sin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00002d46    00000006            : s_sin.c.obj (.text.OUTLINED_FUNCTION_6)
                  00002d4c    00000006            : exit.c.obj (.text:abort)
                  00002d52    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00002d56    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00002d5a    00000004     libc.a : s_sin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00002d5e    00000004            : s_sin.c.obj (.text.OUTLINED_FUNCTION_5)
                  00002d62    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00002d66    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00002d6a    00000006     --HOLE-- [fill = 0]

.cinit     0    00002f48    00000040     
                  00002f48    00000016     (.cinit..data.load) [load image, compression = lzss]
                  00002f5e    00000002     --HOLE-- [fill = 0]
                  00002f60    0000000c     (__TI_handler_table)
                  00002f6c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00002f74    00000010     (__TI_cinit_table)
                  00002f84    00000004     --HOLE-- [fill = 0]

.rodata    0    00002d70    000001d8     
                  00002d70    00000108     libc.a : k_rem_pio2.c.obj (.rodata.ipio2)
                  00002e78    00000040            : k_rem_pio2.c.obj (.rodata.PIo2)
                  00002eb8    00000040            : s_atan.c.obj (.rodata.cst32)
                  00002ef8    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00002f0c    00000010     libc.a : k_rem_pio2.c.obj (.rodata.cst16)
                  00002f1c    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00002f26    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00002f28    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  00002f30    00000008     ti_msp_dl_config.o (.rodata.gPWM_1Config)
                  00002f38    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  00002f3b    00000003     ti_msp_dl_config.o (.rodata.gPWM_1ClockConfig)
                  00002f3e    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00002f41    00000007     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000001fa     UNINITIALIZED
                  20200000    000000bc     (.common:gPWM_0Backup)
                  202000bc    000000bc     (.common:gTIMER_0Backup)
                  20200178    00000078     (.common:gPWM_1Backup)
                  202001f0    00000004     control.o (.bss.TIMA0_IRQHandler.step_arm)
                  202001f4    00000004     control.o (.bss.TIMA0_IRQHandler.step_base)
                  202001f8    00000002     led.o (.bss.LED_Flash.temp)

.data      0    202001fc    00000025     UNINITIALIZED
                  202001fc    00000004     control.o (.data.TIMA0_IRQHandler.last_arm_angle)
                  20200200    00000004     control.o (.data.TIMA0_IRQHandler.last_base_angle)
                  20200204    00000004     control.o (.data.TIMA0_IRQHandler.mode)
                  20200208    00000004     control.o (.data.TIMA0_IRQHandler.real_arm_angle)
                  2020020c    00000004     control.o (.data.TIMA0_IRQHandler.real_base_angle)
                  20200210    00000004     control.o (.data.TIMA0_IRQHandler.target_arm_angle)
                  20200214    00000004     control.o (.data.TIMA0_IRQHandler.target_base_angle)
                  20200218    00000004     control.o (.data.TIMA0_IRQHandler.theta)
                  2020021c    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200220    00000001     control.o (.data.TIMA0_IRQHandler.low_fre)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             1444    57        496    
       startup_mspm0g350x_ticlang.o   8       192       0      
       empty.o                        196     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1648    249       496    
                                                               
    .\Hardware\
       control.o                      782     0         41     
       led.o                          152     0         2      
       board.o                        138     0         0      
       motor.o                        40      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1112    0         43     
                                                               
    D:/ti/mspm0_sdk_2_01_00_03/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     652     0         0      
       dl_uart.o                      90      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         752     0         0      
                                                               
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       k_rem_pio2.c.obj               1652    344       0      
       s_sin.c.obj                    1166    0         0      
       s_atan.c.obj                   784     64        0      
       k_sin.c.obj                    366     0         0      
       k_cos.c.obj                    342     0         0      
       s_floor.c.obj                  324     0         0      
       s_scalbn.c.obj                 216     0         0      
       s_floorf.c.obj                 140     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_roundf.c.obj                 114     0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5404    408       4      
                                                               
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   244     0         0      
       addsf3.S.obj                   232     0         0      
       comparedf2.c.obj               220     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       extendsfdf2.S.obj              64      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               2       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2490    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       58        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   11410   715       1055   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00002f74 records: 2, size/record: 8, table size: 16
	.data: load addr=00002f48, load size=00000016 bytes, run addr=202001fc, run size=00000025 bytes, compression=lzss
	.bss: load addr=00002f6c, load size=00000008 bytes, run addr=20200000, run size=000001fa bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00002f60 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00001013     00002b2c     00002b2a   libc.a : k_rem_pio2.c.obj (.text.OUTLINED_FUNCTION_0)
                             00002d14          : k_rem_pio2.c.obj (.text.OUTLINED_FUNCTION_1)
                             00002d38          : s_sin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00002d3e          : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00002d5c          : s_sin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001009     00002ca0     00002c9c   libc.a : s_sin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00002cbc          : s_sin.c.obj (.text.OUTLINED_FUNCTION_2)
                             00002d4a          : s_sin.c.obj (.text.OUTLINED_FUNCTION_6)
                             00002d60          : s_sin.c.obj (.text.OUTLINED_FUNCTION_5)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   0000178d     00002ce0     00002cde   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00002d2c          : k_cos.c.obj (.text.OUTLINED_FUNCTION_0)
                             00002d32          : k_sin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00002d44          : s_sin.c.obj (.text.OUTLINED_FUNCTION_4)
__aeabi_fadd              $Tramp$TT$L$PI$$__aeabi_fadd
   00001953     00002cfc     00002cf8   libc.a : s_roundf.c.obj (.text.OUTLINED_FUNCTION_0)

[4 trampolines]
[14 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
00002d53  ADC0_IRQHandler                 
00002d53  ADC1_IRQHandler                 
00002d53  AES_IRQHandler                  
00002d56  C$$EXIT                         
00002d53  CANFD0_IRQHandler               
00002d53  DAC0_IRQHandler                 
00002ccd  DL_Common_delayCycles           
00001c81  DL_TimerA_initPWMMode           
00001a21  DL_Timer_initPWMMode            
000016a5  DL_Timer_initTimerMode          
00002975  DL_Timer_setCaptCompUpdateMethod
00002aa1  DL_Timer_setCaptureCompareOutCtl
00002c71  DL_Timer_setCaptureCompareValue 
00002991  DL_Timer_setClockConfig         
00002429  DL_UART_init                    
00002c1b  DL_UART_setClockConfig          
00002d53  DMA_IRQHandler                  
00002d53  Default_Handler                 
00002d53  GROUP0_IRQHandler               
00002d53  GROUP1_IRQHandler               
00002d57  HOSTexit                        
00002d53  HardFault_Handler               
00002d53  I2C0_IRQHandler                 
00002d53  I2C1_IRQHandler                 
000024b9  LED_Flash                       
00002be1  LED_ON                          
00002bf5  LED_Toggle                      
00002d53  NMI_Handler                     
00002d53  PendSV_Handler                  
00002d53  RTC_IRQHandler                  
00002d63  Reset_Handler                   
00002d53  SPI0_IRQHandler                 
00002d53  SPI1_IRQHandler                 
00002d53  SVC_Handler                     
00001d01  SYSCFG_DL_GPIO_init             
0000228d  SYSCFG_DL_PWM_0_init            
00002231  SYSCFG_DL_PWM_1_init            
000028bd  SYSCFG_DL_SYSCTL_init           
00002cbf  SYSCFG_DL_SYSTICK_init          
00002765  SYSCFG_DL_TIMER_0_init          
00002471  SYSCFG_DL_UART_0_init           
000024fd  SYSCFG_DL_init                  
00001d7d  SYSCFG_DL_initPower             
0000286d  Set_PWM                         
00002d53  SysTick_Handler                 
00000e55  TIMA0_IRQHandler                
00002d53  TIMA1_IRQHandler                
00002d53  TIMG0_IRQHandler                
00002d53  TIMG12_IRQHandler               
00002d53  TIMG6_IRQHandler                
00002d53  TIMG7_IRQHandler                
00002d53  TIMG8_IRQHandler                
00002c2d  TI_memcpy_small                 
00002d53  UART0_IRQHandler                
00002d53  UART1_IRQHandler                
00002d53  UART2_IRQHandler                
00002d53  UART3_IRQHandler                
20208000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00002f74  __TI_CINIT_Base                 
00002f84  __TI_CINIT_Limit                
00002f84  __TI_CINIT_Warm                 
00002f60  __TI_Handler_Table_Base         
00002f6c  __TI_Handler_Table_Limit        
00002645  __TI_auto_init_nobinit_nopinit  
00001df9  __TI_decompress_lzss            
00002c3f  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
00002b53  __TI_zero_init_nomemset         
00001013  __adddf3                        
00001953  __addsf3                        
00001ef1  __aeabi_d2f                     
000023dd  __aeabi_d2iz                    
00001013  __aeabi_dadd                    
000020a9  __aeabi_dcmpeq                  
000020e5  __aeabi_dcmpge                  
000020f9  __aeabi_dcmpgt                  
000020d1  __aeabi_dcmple                  
000020bd  __aeabi_dcmplt                  
00001599  __aeabi_ddiv                    
0000178d  __aeabi_dmul                    
00001009  __aeabi_dsub                    
2020021c  __aeabi_errno                   
00002d19  __aeabi_errno_addr              
000025c9  __aeabi_f2d                     
000026f9  __aeabi_f2iz                    
00001953  __aeabi_fadd                    
0000210d  __aeabi_fcmpeq                  
00002149  __aeabi_fcmpge                  
0000215d  __aeabi_fcmpgt                  
00002135  __aeabi_fcmple                  
00002121  __aeabi_fcmplt                  
00001bfd  __aeabi_fdiv                    
00001ae5  __aeabi_fmul                    
00001949  __aeabi_fsub                    
000027f1  __aeabi_i2d                     
000022e5  __aeabi_idiv                    
0000119b  __aeabi_idiv0                   
000022e5  __aeabi_idivmod                 
00002d21  __aeabi_memcpy                  
00002d21  __aeabi_memcpy4                 
00002d21  __aeabi_memcpy8                 
ffffffff  __binit__                       
00001fd9  __cmpdf2                        
00002681  __cmpsf2                        
00001599  __divdf3                        
00001bfd  __divsf3                        
00001fd9  __eqdf2                         
00002681  __eqsf2                         
000025c9  __extendsfdf2                   
000023dd  __fixdfsi                       
000026f9  __fixsfsi                       
000027f1  __floatsidf                     
00001e75  __gedf2                         
00002609  __gesf2                         
00001e75  __gtdf2                         
00002609  __gtsf2                         
00001305  __kernel_cos                    
000000c1  __kernel_rem_pio2               
0000119d  __kernel_sin                    
00001fd9  __ledf2                         
00002681  __lesf2                         
00001fd9  __ltdf2                         
00002681  __ltsf2                         
UNDEFED   __mpu_init                      
0000178d  __muldf3                        
000026bd  __muldsi3                       
00001ae5  __mulsf3                        
00001fd9  __nedf2                         
00002681  __nesf2                         
20207e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
00001009  __subdf3                        
00001949  __subsf3                        
00001ef1  __truncdfsf2                    
00002895  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
00002d67  _system_pre_init                
00002d4d  abort                           
00002171  angle_to_pwm_180                
000021d1  angle_to_pwm_270                
00000b5d  atan                            
00000b5d  atanl                           
00002541  base_x                          
00002585  base_y                          
ffffffff  binit                           
00002b3d  delay_ms                        
00001f65  delay_us                        
00001455  floor                           
00001b71  floorf                          
00001455  floorl                          
20200000  gPWM_0Backup                    
20200178  gPWM_1Backup                    
202000bc  gTIMER_0Backup                  
00000000  interruptVectors                
00001871  ldexp                           
00001871  ldexpl                          
0000233d  main                            
00002041  roundf                          
00001871  scalbn                          
00001871  scalbnl                         
00000705  sin                             
00000705  sinl                            


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  __kernel_rem_pio2               
00000200  __STACK_SIZE                    
00000705  sin                             
00000705  sinl                            
00000b5d  atan                            
00000b5d  atanl                           
00000e55  TIMA0_IRQHandler                
00001009  __aeabi_dsub                    
00001009  __subdf3                        
00001013  __adddf3                        
00001013  __aeabi_dadd                    
0000119b  __aeabi_idiv0                   
0000119d  __kernel_sin                    
00001305  __kernel_cos                    
00001455  floor                           
00001455  floorl                          
00001599  __aeabi_ddiv                    
00001599  __divdf3                        
000016a5  DL_Timer_initTimerMode          
0000178d  __aeabi_dmul                    
0000178d  __muldf3                        
00001871  ldexp                           
00001871  ldexpl                          
00001871  scalbn                          
00001871  scalbnl                         
00001949  __aeabi_fsub                    
00001949  __subsf3                        
00001953  __addsf3                        
00001953  __aeabi_fadd                    
00001a21  DL_Timer_initPWMMode            
00001ae5  __aeabi_fmul                    
00001ae5  __mulsf3                        
00001b71  floorf                          
00001bfd  __aeabi_fdiv                    
00001bfd  __divsf3                        
00001c81  DL_TimerA_initPWMMode           
00001d01  SYSCFG_DL_GPIO_init             
00001d7d  SYSCFG_DL_initPower             
00001df9  __TI_decompress_lzss            
00001e75  __gedf2                         
00001e75  __gtdf2                         
00001ef1  __aeabi_d2f                     
00001ef1  __truncdfsf2                    
00001f65  delay_us                        
00001fd9  __cmpdf2                        
00001fd9  __eqdf2                         
00001fd9  __ledf2                         
00001fd9  __ltdf2                         
00001fd9  __nedf2                         
00002041  roundf                          
000020a9  __aeabi_dcmpeq                  
000020bd  __aeabi_dcmplt                  
000020d1  __aeabi_dcmple                  
000020e5  __aeabi_dcmpge                  
000020f9  __aeabi_dcmpgt                  
0000210d  __aeabi_fcmpeq                  
00002121  __aeabi_fcmplt                  
00002135  __aeabi_fcmple                  
00002149  __aeabi_fcmpge                  
0000215d  __aeabi_fcmpgt                  
00002171  angle_to_pwm_180                
000021d1  angle_to_pwm_270                
00002231  SYSCFG_DL_PWM_1_init            
0000228d  SYSCFG_DL_PWM_0_init            
000022e5  __aeabi_idiv                    
000022e5  __aeabi_idivmod                 
0000233d  main                            
000023dd  __aeabi_d2iz                    
000023dd  __fixdfsi                       
00002429  DL_UART_init                    
00002471  SYSCFG_DL_UART_0_init           
000024b9  LED_Flash                       
000024fd  SYSCFG_DL_init                  
00002541  base_x                          
00002585  base_y                          
000025c9  __aeabi_f2d                     
000025c9  __extendsfdf2                   
00002609  __gesf2                         
00002609  __gtsf2                         
00002645  __TI_auto_init_nobinit_nopinit  
00002681  __cmpsf2                        
00002681  __eqsf2                         
00002681  __lesf2                         
00002681  __ltsf2                         
00002681  __nesf2                         
000026bd  __muldsi3                       
000026f9  __aeabi_f2iz                    
000026f9  __fixsfsi                       
00002765  SYSCFG_DL_TIMER_0_init          
000027f1  __aeabi_i2d                     
000027f1  __floatsidf                     
0000286d  Set_PWM                         
00002895  _c_int00_noargs                 
000028bd  SYSCFG_DL_SYSCTL_init           
00002975  DL_Timer_setCaptCompUpdateMethod
00002991  DL_Timer_setClockConfig         
00002aa1  DL_Timer_setCaptureCompareOutCtl
00002b3d  delay_ms                        
00002b53  __TI_zero_init_nomemset         
00002be1  LED_ON                          
00002bf5  LED_Toggle                      
00002c1b  DL_UART_setClockConfig          
00002c2d  TI_memcpy_small                 
00002c3f  __TI_decompress_none            
00002c71  DL_Timer_setCaptureCompareValue 
00002cbf  SYSCFG_DL_SYSTICK_init          
00002ccd  DL_Common_delayCycles           
00002d19  __aeabi_errno_addr              
00002d21  __aeabi_memcpy                  
00002d21  __aeabi_memcpy4                 
00002d21  __aeabi_memcpy8                 
00002d4d  abort                           
00002d53  ADC0_IRQHandler                 
00002d53  ADC1_IRQHandler                 
00002d53  AES_IRQHandler                  
00002d53  CANFD0_IRQHandler               
00002d53  DAC0_IRQHandler                 
00002d53  DMA_IRQHandler                  
00002d53  Default_Handler                 
00002d53  GROUP0_IRQHandler               
00002d53  GROUP1_IRQHandler               
00002d53  HardFault_Handler               
00002d53  I2C0_IRQHandler                 
00002d53  I2C1_IRQHandler                 
00002d53  NMI_Handler                     
00002d53  PendSV_Handler                  
00002d53  RTC_IRQHandler                  
00002d53  SPI0_IRQHandler                 
00002d53  SPI1_IRQHandler                 
00002d53  SVC_Handler                     
00002d53  SysTick_Handler                 
00002d53  TIMA1_IRQHandler                
00002d53  TIMG0_IRQHandler                
00002d53  TIMG12_IRQHandler               
00002d53  TIMG6_IRQHandler                
00002d53  TIMG7_IRQHandler                
00002d53  TIMG8_IRQHandler                
00002d53  UART0_IRQHandler                
00002d53  UART1_IRQHandler                
00002d53  UART2_IRQHandler                
00002d53  UART3_IRQHandler                
00002d56  C$$EXIT                         
00002d57  HOSTexit                        
00002d63  Reset_Handler                   
00002d67  _system_pre_init                
00002f60  __TI_Handler_Table_Base         
00002f6c  __TI_Handler_Table_Limit        
00002f74  __TI_CINIT_Base                 
00002f84  __TI_CINIT_Limit                
00002f84  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gPWM_0Backup                    
202000bc  gTIMER_0Backup                  
20200178  gPWM_1Backup                    
2020021c  __aeabi_errno                   
20207e00  __stack                         
20208000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[178 symbols]
