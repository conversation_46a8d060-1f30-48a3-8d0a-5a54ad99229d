/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --package "LQFP-64(PM)" --part "Default" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.20.0+3587"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const PWM     = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1    = PWM.addInstance();
const PWM2    = PWM.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const TIMER   = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1  = TIMER.addInstance();
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const gate7  = system.clockTree["MFCLKGATE"];
gate7.enable = true;

GPIO1.$name                              = "KEY";
GPIO1.port                               = "PORTA";
GPIO1.associatedPins[0].direction        = "INPUT";
GPIO1.associatedPins[0].polarity         = "RISE";
GPIO1.associatedPins[0].$name            = "Pin_18";
GPIO1.associatedPins[0].assignedPin      = "18";
GPIO1.associatedPins[0].internalResistor = "PULL_DOWN";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.$name                         = "LED";
GPIO2.associatedPins[0].$name       = "led";
GPIO2.associatedPins[0].pin.$assign = "PB9";

PWM1.$name                      = "PWM_0";
PWM1.timerStartTimer            = true;
PWM1.pwmMode                    = "EDGE_ALIGN_UP";
PWM1.timerCount                 = 10000;
PWM1.ccIndex                    = [0];
PWM1.clockPrescale              = 64;
PWM1.peripheral.ccp0Pin.$assign = "PB17";
PWM1.PWM_CHANNEL_0.$name        = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_0.initVal      = "HIGH";
PWM1.ccp0PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric2";

PWM2.$name                      = "PWM_1";
PWM2.timerCount                 = 10000;
PWM2.timerStartTimer            = true;
PWM2.pwmMode                    = "EDGE_ALIGN_UP";
PWM2.ccIndex                    = [1];
PWM2.clockPrescale              = 64;
PWM2.peripheral.ccp1Pin.$assign = "PB16";
PWM2.ccp1PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric4";
PWM2.PWM_CHANNEL_1.$name        = "ti_driverlib_pwm_PWMTimerCC1";
PWM2.PWM_CHANNEL_1.initVal      = "HIGH";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

SYSTICK.periodEnable      = true;
SYSTICK.systickEnable     = true;
SYSTICK.interruptPriority = "0";
SYSTICK.period            = 32;

TIMER1.$name            = "TIMER_0";
TIMER1.timerClkDiv      = 8;
TIMER1.timerClkPrescale = 200;
TIMER1.timerStartTimer  = true;
TIMER1.timerMode        = "PERIODIC";
TIMER1.interrupts       = ["ZERO"];
TIMER1.timerPeriod      = "10 ms";

UART1.$name                    = "UART_0";
UART1.enabledInterrupts        = ["RX"];
UART1.uartClkSrc               = "MFCLK";
UART1.peripheral.$assign       = "UART0";
UART1.peripheral.rxPin.$assign = "PA11";
UART1.peripheral.txPin.$assign = "PA10";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
GPIO1.associatedPins[0].pin.$suggestSolution = "PA18";
Board.peripheral.$suggestSolution            = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution   = "PA20";
Board.peripheral.swdioPin.$suggestSolution   = "PA19";
PWM1.peripheral.$suggestSolution             = "TIMA1";
PWM2.peripheral.$suggestSolution             = "TIMG7";
TIMER1.peripheral.$suggestSolution           = "TIMA0";
